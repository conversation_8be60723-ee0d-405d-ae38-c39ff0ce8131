import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useDocuments } from '@/contexts/DocumentContext';
import { DocumentFormData, DOCUMENT_STATUSES, DOCUMENT_ACCESS_LEVELS } from '@/models/document';
import { ChevronLeft, ChevronRight, Save, Edit, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Step components
import BasicInfoStep from './FormSteps/BasicInfoStep';
import ApprovalStep from './FormSteps/ApprovalStep';
import FilesStep from './FormSteps/FilesStep';

const documentSchema = z.object({
  documentReferenceNumber: z.string().min(1, 'Document Reference Number is required'),
  documentTitle: z.string().min(1, 'Document Title is required'),
  version: z.number().min(0, 'Version must be a positive number'),
  issueDate: z.string().min(1, 'Issue Date is required'),
  authorFunction: z.string().min(1, 'Author Function is required'),
  responsibleDepartment: z.string().min(1, 'Responsible Department is required'),
  status: z.string().min(1, 'Status is required'),
  approvalDate: z.string().optional().or(z.literal('')),
  approvedByFunction: z.string().optional().or(z.literal('')),
  revisionDate: z.string().optional().or(z.literal('')),
  revisionReason: z.string().optional().or(z.literal('')),
  previousReferenceNumber: z.string().optional().or(z.literal('')),
  storageLocation: z.string().min(1, 'Storage Location is required'),
  distributionMethod: z.string().optional().or(z.literal('')),
  remarks: z.string().optional().or(z.literal('')),
  accessLevel: z.string().min(1, 'Access Level is required'),
});

interface FormWizardProps {
  className?: string;
}

const FormWizard: React.FC<FormWizardProps> = ({ className }) => {
  const { 
    addDocument, 
    updateDocument, 
    currentEditId, 
    setCurrentEditId, 
    documents,
    isLoading 
  } = useDocuments();
  
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [currentFile, setCurrentFile] = useState<File | null>(null);

  const form = useForm<DocumentFormData>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      documentReferenceNumber: '',
      documentTitle: '',
      version: 1,
      issueDate: '',
      authorFunction: '',
      responsibleDepartment: '',
      status: '',
      storageLocation: '',
      accessLevel: '',
      approvalDate: '',
      approvedByFunction: '',
      revisionDate: '',
      revisionReason: '',
      previousReferenceNumber: '',
      distributionMethod: '',
      remarks: ''
    }
  });

  const steps = [
    {
      id: 1,
      title: 'Basic Information',
      description: 'Document details and identification',
      fields: ['documentReferenceNumber', 'documentTitle', 'version', 'issueDate', 'authorFunction', 'responsibleDepartment', 'status', 'storageLocation', 'accessLevel'] as (keyof DocumentFormData)[]
    },
    {
      id: 2,
      title: 'Approval & Revision',
      description: 'Validation and revision details',
      fields: ['approvalDate', 'approvedByFunction', 'revisionDate', 'revisionReason', 'previousReferenceNumber', 'distributionMethod'] as (keyof DocumentFormData)[]
    },
    {
      id: 3,
      title: 'Files & Notes',
      description: 'File attachments and additional remarks',
      fields: ['remarks'] as (keyof DocumentFormData)[]
    }
  ];

  // Load document data when editing
  React.useEffect(() => {
    if (currentEditId) {
      const document = documents.find(doc => doc.id === currentEditId);
      if (document) {
        form.reset({
          documentReferenceNumber: document.documentReferenceNumber,
          documentTitle: document.documentTitle,
          version: document.version,
          issueDate: document.issueDate,
          authorFunction: document.authorFunction,
          responsibleDepartment: document.responsibleDepartment,
          status: document.status,
          storageLocation: document.storageLocation,
          accessLevel: document.accessLevel,
          approvalDate: document.approvalDate || '',
          approvedByFunction: document.approvedByFunction || '',
          revisionDate: document.revisionDate || '',
          revisionReason: document.revisionReason || '',
          previousReferenceNumber: document.previousReferenceNumber || '',
          distributionMethod: document.distributionMethod || '',
          remarks: document.remarks || ''
        });
      }
    }
  }, [currentEditId, documents, form]);

  const validateCurrentStep = async () => {
    const currentStepData = steps[currentStep - 1];
    
    if (!currentStepData) {
      console.error('Invalid step:', currentStep);
      return false;
    }

    // Define which fields are required for each step
    const requiredFieldsByStep: Record<number, (keyof DocumentFormData)[]> = {
      1: ['documentReferenceNumber', 'documentTitle', 'version', 'issueDate', 'authorFunction', 'responsibleDepartment', 'status', 'storageLocation', 'accessLevel'],
      2: [], // All fields in step 2 are optional
      3: [] // All fields in step 3 are optional
    };

    const requiredFields = requiredFieldsByStep[currentStep] || [];

    // Only validate required fields for the current step
    if (requiredFields.length > 0) {
      const result = await form.trigger(requiredFields);

      if (!result) {
        const errors = form.formState.errors;
        const stepErrors = requiredFields.filter(field => errors[field]);

        if (stepErrors.length > 0) {
          toast({
            title: "Validation Error",
            description: `Please fill out all required fields: ${stepErrors.join(', ')}`,
            variant: "destructive",
          });
        }
        return false;
      }
    }

    // For optional fields, just trigger validation to clear any existing errors
    // but don't block progression if they fail
    await form.trigger(currentStepData.fields);

    return true;
  };

  const handleNext = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < steps.length) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleStepClick = async (stepId: number) => {
    // Allow navigation to previous steps or current step
    if (stepId <= currentStep) {
      setCurrentStep(stepId);
    } else {
      // For forward navigation, validate current step first
      const isValid = await validateCurrentStep();
      if (isValid) {
        setCurrentStep(stepId);
      }
    }
  };

  // Debug function to help troubleshoot validation issues
  const debugFormState = () => {
    const formValues = form.getValues();
    const errors = form.formState.errors;
    const currentStepData = steps[currentStep - 1];

    console.log('=== FORM DEBUG INFO ===');
    console.log('Current Step:', currentStep);
    console.log('Current Step Fields:', currentStepData?.fields);
    console.log('Form Values:', formValues);
    console.log('Form Errors:', errors);
    console.log('Form Valid:', form.formState.isValid);

    toast({
      title: "Debug Info",
      description: `Check console for detailed form state. Current step: ${currentStep}`,
    });
  };

  const onSubmit = async (data: DocumentFormData) => {
    try {
      const documentData = {
        documentReferenceNumber: data.documentReferenceNumber,
        documentTitle: data.documentTitle,
        version: data.version,
        issueDate: data.issueDate,
        authorFunction: data.authorFunction,
        responsibleDepartment: data.responsibleDepartment,
        status: data.status === '' ? DOCUMENT_STATUSES[0] : data.status,
        approvalDate: data.approvalDate,
        approvedByFunction: data.approvedByFunction,
        revisionDate: data.revisionDate,
        revisionReason: data.revisionReason,
        previousReferenceNumber: data.previousReferenceNumber,
        storageLocation: data.storageLocation,
        distributionMethod: data.distributionMethod,
        remarks: data.remarks,
        accessLevel: data.accessLevel === '' ? DOCUMENT_ACCESS_LEVELS[0] : data.accessLevel,
        fileName: currentFile?.name || '',
        fileData: currentFile ? URL.createObjectURL(currentFile) : '',
      };

      if (currentEditId) {
        updateDocument(currentEditId, documentData);
      } else {
        addDocument(documentData);
      }

      handleClear();
    } catch (error) {
      console.error('Error saving document:', error);
      toast({
        title: "Error",
        description: "Failed to save document. Please check the form data and try again.",
        variant: "destructive",
      });
    }
  };

  const handleClear = () => {
    form.reset();
    setCurrentEditId(null);
    setCurrentFile(null);
    setCurrentStep(1);
  };

  const progress = (currentStep / steps.length) * 100;

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoStep form={form} />;
      case 2:
        return <ApprovalStep form={form} />;
      case 3:
        return (
          <FilesStep
            form={form}
            currentFile={currentFile}
            onFileChange={setCurrentFile}
          />
        );
      default:
        return <BasicInfoStep form={form} />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {currentEditId ? (
            <>
              <Edit className="h-5 w-5" />
              Edit Document
            </>
          ) : (
            <>
              <Plus className="h-5 w-5" />
              Add New Document
            </>
          )}
        </CardTitle>
        
        {/* Progress Indicator */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Step {currentStep} of {steps.length}</span>
            <span>{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
          
          {/* Step Labels */}
          <div className="flex justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`text-center flex-1 ${index < steps.length - 1 ? 'border-r border-muted' : ''}`}
              >
                <button
                  type="button"
                  onClick={() => handleStepClick(step.id)}
                  className={`w-full p-2 rounded-md transition-colors duration-150 ${
                    step.id <= currentStep
                      ? 'hover:bg-muted/50 cursor-pointer'
                      : 'cursor-not-allowed opacity-60'
                  }`}
                  disabled={step.id > currentStep}
                >
                  <div className={`text-sm font-medium ${currentStep === step.id ? 'text-primary' : currentStep > step.id ? 'text-green-600' : 'text-muted-foreground'}`}>
                    {step.title}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {step.description}
                  </div>
                </button>
              </div>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Current Step Content */}
            {renderCurrentStep()}

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <div>
                {currentStep > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handlePrevious}
                    disabled={isLoading}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>
                )}
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClear}
                  disabled={isLoading}
                >
                  Clear
                </Button>

                {/* Debug button - temporary for troubleshooting */}
                <Button
                  type="button"
                  variant="secondary"
                  onClick={debugFormState}
                  size="sm"
                >
                  Debug
                </Button>

                {currentStep < steps.length ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    disabled={isLoading}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={isLoading}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {currentEditId ? 'Update Document' : 'Save Document'}
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default FormWizard;